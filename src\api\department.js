import { sendSoapRequest } from './request.js'
import { API_ACTIONS } from './config.js'

/**
 * 科室相关API
 * 根据接口文档实现科室查询功能
 */

/**
 * 查询科室列表 (1012)
 * @param {Object} params - 查询参数
 * @param {string} params.hospitalId - 医院唯一编号 (默认: SGSDYRMYY)
 * @param {string} params.extUserId - 操作员代码 (默认: NOVA001)
 * @param {string} params.extOrgCode - 预约机构 (可选)
 * @param {string} params.clientType - 客户端类型 (可选)
 * @param {string} params.departmentType - 科室类别 (可选)
 * @param {string} params.departmentCode - 科室代码 (可选)
 * @param {string} params.departmentGroupCode - 一级科室代码 (可选)
 * @returns {Promise<Object>} 科室查询结果
 */
export async function queryDepartment(params = {}) {
  try {
    const {
      hospitalId = 'SGSDYRMYY',
      extUserId = 'NOVA001',
      extOrgCode = '',
      clientType = '',
      departmentType = '',
      departmentCode = '',
      departmentGroupCode = ''
    } = params

    // 构建请求消息XML
    const requestMessage = `<Request>
  <TradeCode>1012</TradeCode>
  <ExtOrgCode>${extOrgCode}</ExtOrgCode>
  <ClientType>${clientType}</ClientType>
  <HospitalId>${hospitalId}</HospitalId>
  <DepartmentType>${departmentType}</DepartmentType>
  <DepartmentCode>${departmentCode}</DepartmentCode>
  <DepartmentGroupCode>${departmentGroupCode}</DepartmentGroupCode>
  <ExtUserID>${extUserId}</ExtUserID>
</Request>`

    console.log('科室查询请求参数:', params)

    // 发送SOAP请求
    const result = await sendSoapRequest(API_ACTIONS.QUERY_DEPARTMENT, requestMessage)

    if (result.success) {
      console.log('科室查询成功:', result.data)
      return {
        success: true,
        data: result.data,
        message: '科室查询成功'
      }
    } else {
      console.error('科室查询失败:', result.error)
      return {
        success: false,
        error: result.error,
        message: result.message || '科室查询失败'
      }
    }
  } catch (error) {
    console.error('科室查询异常:', error)
    return {
      success: false,
      error: '查询异常',
      message: error.message || '科室查询过程中发生异常'
    }
  }
}

/**
 * 获取所有科室列表 (使用默认参数)
 * @returns {Promise<Object>} 所有科室列表
 */
export async function getAllDepartments() {
  return await queryDepartment()
}

/**
 * 根据科室类型查询科室
 * @param {string} departmentType - 科室类型
 * @returns {Promise<Object>} 科室查询结果
 */
export async function queryDepartmentByType(departmentType) {
  return await queryDepartment({ departmentType })
}

/**
 * 根据一级科室代码查询下级科室
 * @param {string} departmentGroupCode - 一级科室代码
 * @returns {Promise<Object>} 科室查询结果
 */
export async function querySubDepartments(departmentGroupCode) {
  return await queryDepartment({ departmentGroupCode })
}

// 导出所有API方法
export default {
  queryDepartment,
  getAllDepartments,
  queryDepartmentByType,
  querySubDepartments
}
