/**
 * API测试文件
 * 用于测试SOAP API封装是否正常工作
 */

import { API } from './index.js'

/**
 * 测试基本物价查询功能
 */
export async function testBasicPriceQuery() {
  console.log('=== 测试基本物价查询 ===')
  
  try {
    const result = await API.price.queryPrice('MR', '9100')
    
    console.log('查询结果:', result)
    
    if (result.success) {
      console.log('✅ 基本物价查询测试通过')
      return true
    } else {
      console.log('❌ 基本物价查询测试失败:', result.message)
      return false
    }
  } catch (error) {
    console.error('❌ 基本物价查询测试异常:', error.message)
    return false
  }
}

/**
 * 测试默认物价查询功能
 */
export async function testDefaultPriceQuery() {
  console.log('=== 测试默认物价查询 ===')
  
  try {
    const result = await API.price.getDefaultPrice()
    
    console.log('默认查询结果:', result)
    
    if (result.success) {
      console.log('✅ 默认物价查询测试通过')
      return true
    } else {
      console.log('❌ 默认物价查询测试失败:', result.message)
      return false
    }
  } catch (error) {
    console.error('❌ 默认物价查询测试异常:', error.message)
    return false
  }
}

/**
 * 测试批量物价查询功能
 */
export async function testBatchPriceQuery() {
  console.log('=== 测试批量物价查询 ===')
  
  try {
    const queries = [
      { alias: 'MR', tradeCode: '9100' },
      { alias: 'CT', tradeCode: '9101' }
    ]
    
    const results = await API.price.batchQueryPrice(queries)
    
    console.log('批量查询结果:', results)
    
    const successCount = results.filter(item => item.result.success).length
    
    if (successCount > 0) {
      console.log(`✅ 批量物价查询测试通过 (成功: ${successCount}/${results.length})`)
      return true
    } else {
      console.log('❌ 批量物价查询测试失败: 所有查询都失败了')
      return false
    }
  } catch (error) {
    console.error('❌ 批量物价查询测试异常:', error.message)
    return false
  }
}

/**
 * 测试科室物价查询功能
 */
export async function testDepartmentPriceQuery() {
  console.log('=== 测试科室物价查询 ===')
  
  try {
    const result = await API.price.queryPriceByDepartment('MR')
    
    console.log('科室查询结果:', result)
    
    if (result.success) {
      console.log('✅ 科室物价查询测试通过')
      return true
    } else {
      console.log('❌ 科室物价查询测试失败:', result.message)
      return false
    }
  } catch (error) {
    console.error('❌ 科室物价查询测试异常:', error.message)
    return false
  }
}

/**
 * 测试自定义SOAP请求功能
 */
export async function testCustomSoapRequest() {
  console.log('=== 测试自定义SOAP请求 ===')
  
  try {
    const customMessage = '<Request><Alias>TEST</Alias><TradeCode>9999</TradeCode></Request>'
    const result = await API.soap.sendRequest('MES0061', customMessage)
    
    console.log('自定义请求结果:', result)
    
    if (result.success) {
      console.log('✅ 自定义SOAP请求测试通过')
      return true
    } else {
      console.log('❌ 自定义SOAP请求测试失败:', result.message)
      return false
    }
  } catch (error) {
    console.error('❌ 自定义SOAP请求测试异常:', error.message)
    return false
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('🚀 开始运行API测试套件...\n')
  
  const tests = [
    { name: '基本物价查询', fn: testBasicPriceQuery },
    { name: '默认物价查询', fn: testDefaultPriceQuery },
    { name: '批量物价查询', fn: testBatchPriceQuery },
    { name: '科室物价查询', fn: testDepartmentPriceQuery },
    { name: '自定义SOAP请求', fn: testCustomSoapRequest }
  ]
  
  const results = []
  
  for (const test of tests) {
    console.log(`\n📋 运行测试: ${test.name}`)
    const result = await test.fn()
    results.push({ name: test.name, passed: result })
    
    // 测试间隔，避免请求过于频繁
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  // 输出测试总结
  console.log('\n📊 测试结果总结:')
  console.log('==================')
  
  const passedTests = results.filter(r => r.passed)
  const failedTests = results.filter(r => !r.passed)
  
  results.forEach(result => {
    const status = result.passed ? '✅' : '❌'
    console.log(`${status} ${result.name}`)
  })
  
  console.log(`\n总计: ${results.length} 个测试`)
  console.log(`通过: ${passedTests.length} 个`)
  console.log(`失败: ${failedTests.length} 个`)
  console.log(`成功率: ${Math.round(passedTests.length / results.length * 100)}%`)
  
  if (failedTests.length === 0) {
    console.log('\n🎉 所有测试都通过了！API封装工作正常。')
  } else {
    console.log('\n⚠️  部分测试失败，请检查网络连接和服务器状态。')
  }
  
  return {
    total: results.length,
    passed: passedTests.length,
    failed: failedTests.length,
    results: results
  }
}

// 导出测试函数
export default {
  testBasicPriceQuery,
  testDefaultPriceQuery,
  testBatchPriceQuery,
  testDepartmentPriceQuery,
  testCustomSoapRequest,
  runAllTests
}
