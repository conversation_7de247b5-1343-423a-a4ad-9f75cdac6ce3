/**
 * API使用示例
 * 展示如何在Vue组件中使用封装好的SOAP API
 */

import { API } from './index.js'

// 示例1: 基本物价查询
export async function exampleBasicPriceQuery() {
  try {
    console.log('=== 基本物价查询示例 ===')
    
    const result = await API.price.queryPrice('MR', '9100')
    
    if (result.success) {
      console.log('查询成功:', result.data)
      return result.data
    } else {
      console.error('查询失败:', result.message)
      return null
    }
  } catch (error) {
    console.error('查询异常:', error)
    return null
  }
}

// 示例2: 使用默认参数查询
export async function exampleDefaultPriceQuery() {
  try {
    console.log('=== 默认物价查询示例 ===')
    
    const result = await API.price.getDefaultPrice()
    
    if (result.success) {
      console.log('默认查询成功:', result.data)
      return result.data
    } else {
      console.error('默认查询失败:', result.message)
      return null
    }
  } catch (error) {
    console.error('默认查询异常:', error)
    return null
  }
}

// 示例3: 批量查询
export async function exampleBatchPriceQuery() {
  try {
    console.log('=== 批量物价查询示例 ===')
    
    const queries = [
      { alias: 'MR', tradeCode: '9100' },
      { alias: 'CT', tradeCode: '9101' },
      { alias: 'DR', tradeCode: '9102' }
    ]
    
    const results = await API.price.batchQueryPrice(queries)
    
    console.log('批量查询结果:', results)
    
    // 处理批量查询结果
    const successResults = results.filter(item => item.result.success)
    const failedResults = results.filter(item => !item.result.success)
    
    console.log(`成功: ${successResults.length}, 失败: ${failedResults.length}`)
    
    return {
      success: successResults,
      failed: failedResults,
      total: results.length
    }
  } catch (error) {
    console.error('批量查询异常:', error)
    return null
  }
}

// 示例4: 按科室查询
export async function exampleDepartmentPriceQuery() {
  try {
    console.log('=== 科室物价查询示例 ===')
    
    const departments = ['MR', 'CT', 'DR']
    const results = {}
    
    for (const dept of departments) {
      const result = await API.price.queryPriceByDepartment(dept)
      results[dept] = result
      
      if (result.success) {
        console.log(`${dept}科室查询成功:`, result.data)
      } else {
        console.error(`${dept}科室查询失败:`, result.message)
      }
    }
    
    return results
  } catch (error) {
    console.error('科室查询异常:', error)
    return null
  }
}

// 示例5: 自定义SOAP请求
export async function exampleCustomSoapRequest() {
  try {
    console.log('=== 自定义SOAP请求示例 ===')
    
    // 自定义请求消息
    const customMessage = '<Request><Alias>CUSTOM</Alias><TradeCode>9999</TradeCode><Extra>test</Extra></Request>'
    
    const result = await API.soap.sendRequest('MES0061', customMessage)
    
    if (result.success) {
      console.log('自定义请求成功:', result.data)
      return result.data
    } else {
      console.error('自定义请求失败:', result.message)
      return null
    }
  } catch (error) {
    console.error('自定义请求异常:', error)
    return null
  }
}

// Vue组件中的使用示例
export const vueComponentExample = {
  data() {
    return {
      priceData: null,
      loading: false,
      error: null
    }
  },
  
  methods: {
    // 在Vue组件方法中使用API
    async fetchPriceData() {
      this.loading = true
      this.error = null
      
      try {
        const result = await API.price.queryPrice('MR', '9100')
        
        if (result.success) {
          this.priceData = result.data
          this.$message.success('物价数据获取成功')
        } else {
          this.error = result.message
          this.$message.error(`获取失败: ${result.message}`)
        }
      } catch (error) {
        this.error = error.message
        this.$message.error(`请求异常: ${error.message}`)
      } finally {
        this.loading = false
      }
    },
    
    // 处理批量查询
    async fetchBatchPriceData() {
      this.loading = true
      
      try {
        const queries = [
          { alias: 'MR', tradeCode: '9100' },
          { alias: 'CT', tradeCode: '9101' }
        ]
        
        const results = await API.price.batchQueryPrice(queries)
        
        // 处理结果
        const successCount = results.filter(item => item.result.success).length
        this.$message.success(`批量查询完成，成功: ${successCount}/${results.length}`)
        
        return results
      } catch (error) {
        this.$message.error(`批量查询失败: ${error.message}`)
      } finally {
        this.loading = false
      }
    }
  },
  
  // 组件挂载时自动获取数据
  async mounted() {
    await this.fetchPriceData()
  }
}

// 导出所有示例函数
export default {
  exampleBasicPriceQuery,
  exampleDefaultPriceQuery,
  exampleBatchPriceQuery,
  exampleDepartmentPriceQuery,
  exampleCustomSoapRequest,
  vueComponentExample
}
