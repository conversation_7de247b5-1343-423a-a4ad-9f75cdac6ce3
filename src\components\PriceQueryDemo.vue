<template>
  <div class="price-query-demo">
    <el-card class="demo-card">
      <div slot="header" class="card-header">
        <span>物价查询演示</span>
        <el-button 
          type="primary" 
          size="small" 
          @click="runTests"
          :loading="testRunning"
        >
          运行API测试
        </el-button>
      </div>

      <!-- 基本查询 -->
      <el-row :gutter="20" class="demo-section">
        <el-col :span="12">
          <h4>基本物价查询</h4>
          <el-form :inline="true" size="small">
            <el-form-item label="别名:">
              <el-input v-model="queryForm.alias" placeholder="MR" style="width: 80px;"></el-input>
            </el-form-item>
            <el-form-item label="交易代码:">
              <el-input v-model="queryForm.tradeCode" placeholder="9100" style="width: 80px;"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button 
                type="primary" 
                @click="queryPrice" 
                :loading="loading.basic"
                size="small"
              >
                查询
              </el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="12">
          <h4>科室查询</h4>
          <el-form :inline="true" size="small">
            <el-form-item label="科室:">
              <el-select v-model="selectedDepartment" placeholder="选择科室" size="small">
                <el-option label="磁共振科(MR)" value="MR"></el-option>
                <el-option label="CT科(CT)" value="CT"></el-option>
                <el-option label="DR科(DR)" value="DR"></el-option>
                <el-option label="超声科(US)" value="US"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button 
                type="success" 
                @click="queryByDepartment" 
                :loading="loading.department"
                size="small"
              >
                查询
              </el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <!-- 批量查询 -->
      <el-row class="demo-section">
        <el-col :span="24">
          <h4>批量查询</h4>
          <el-button 
            type="warning" 
            @click="batchQuery" 
            :loading="loading.batch"
            size="small"
          >
            批量查询多个科室
          </el-button>
        </el-col>
      </el-row>

      <!-- 结果显示 -->
      <el-row class="demo-section" v-if="results.length > 0">
        <el-col :span="24">
          <h4>查询结果</h4>
          <el-table :data="results" border size="small" max-height="300">
            <el-table-column prop="type" label="查询类型" width="120"></el-table-column>
            <el-table-column prop="params" label="查询参数" width="150"></el-table-column>
            <el-table-column prop="status" label="状态" width="80">
              <template slot-scope="scope">
                <el-tag :type="scope.row.success ? 'success' : 'danger'" size="mini">
                  {{ scope.row.success ? '成功' : '失败' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="message" label="消息"></el-table-column>
            <el-table-column label="数据" width="200">
              <template slot-scope="scope">
                <el-button 
                  type="text" 
                  size="mini" 
                  @click="showData(scope.row.data)"
                  v-if="scope.row.data"
                >
                  查看数据
                </el-button>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>

      <!-- 测试结果 -->
      <el-row class="demo-section" v-if="testResults">
        <el-col :span="24">
          <h4>API测试结果</h4>
          <el-alert
            :title="`测试完成: ${testResults.passed}/${testResults.total} 通过`"
            :type="testResults.failed === 0 ? 'success' : 'warning'"
            show-icon
            :closable="false"
          >
            <div slot="default">
              <p>成功率: {{ Math.round(testResults.passed / testResults.total * 100) }}%</p>
              <el-button type="text" @click="showTestDetails = !showTestDetails">
                {{ showTestDetails ? '隐藏' : '显示' }}详细结果
              </el-button>
            </div>
          </el-alert>
          
          <el-collapse v-if="showTestDetails" class="test-details">
            <el-collapse-item 
              v-for="(result, index) in testResults.results" 
              :key="index"
              :title="`${result.passed ? '✅' : '❌'} ${result.name}`"
            >
              <p>状态: {{ result.passed ? '通过' : '失败' }}</p>
            </el-collapse-item>
          </el-collapse>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据详情对话框 -->
    <el-dialog title="查询数据详情" :visible.sync="dataDialogVisible" width="60%">
      <pre>{{ JSON.stringify(selectedData, null, 2) }}</pre>
    </el-dialog>
  </div>
</template>

<script>
import { API } from '@/api'
import apiTest from '@/api/test'

export default {
  name: 'PriceQueryDemo',
  data() {
    return {
      // 查询表单
      queryForm: {
        alias: 'MR',
        tradeCode: '9100'
      },
      selectedDepartment: 'MR',
      
      // 加载状态
      loading: {
        basic: false,
        department: false,
        batch: false
      },
      testRunning: false,
      
      // 结果数据
      results: [],
      testResults: null,
      showTestDetails: false,
      
      // 对话框
      dataDialogVisible: false,
      selectedData: null
    }
  },
  methods: {
    // 基本物价查询
    async queryPrice() {
      this.loading.basic = true
      try {
        const result = await API.price.queryPrice(
          this.queryForm.alias, 
          this.queryForm.tradeCode
        )
        
        this.addResult({
          type: '基本查询',
          params: `${this.queryForm.alias}, ${this.queryForm.tradeCode}`,
          success: result.success,
          message: result.message,
          data: result.data
        })
        
        if (result.success) {
          this.$message.success('查询成功')
        } else {
          this.$message.error(`查询失败: ${result.message}`)
        }
      } catch (error) {
        this.$message.error(`查询异常: ${error.message}`)
        this.addResult({
          type: '基本查询',
          params: `${this.queryForm.alias}, ${this.queryForm.tradeCode}`,
          success: false,
          message: error.message,
          data: null
        })
      } finally {
        this.loading.basic = false
      }
    },
    
    // 科室查询
    async queryByDepartment() {
      this.loading.department = true
      try {
        const result = await API.price.queryPriceByDepartment(this.selectedDepartment)
        
        this.addResult({
          type: '科室查询',
          params: this.selectedDepartment,
          success: result.success,
          message: result.message,
          data: result.data
        })
        
        if (result.success) {
          this.$message.success('科室查询成功')
        } else {
          this.$message.error(`科室查询失败: ${result.message}`)
        }
      } catch (error) {
        this.$message.error(`科室查询异常: ${error.message}`)
      } finally {
        this.loading.department = false
      }
    },
    
    // 批量查询
    async batchQuery() {
      this.loading.batch = true
      try {
        const queries = [
          { alias: 'MR', tradeCode: '9100' },
          { alias: 'CT', tradeCode: '9101' },
          { alias: 'DR', tradeCode: '9102' }
        ]
        
        const results = await API.price.batchQueryPrice(queries)
        
        results.forEach((item, index) => {
          this.addResult({
            type: '批量查询',
            params: `${item.query.alias}, ${item.query.tradeCode}`,
            success: item.result.success,
            message: item.result.message,
            data: item.result.data
          })
        })
        
        const successCount = results.filter(item => item.result.success).length
        this.$message.success(`批量查询完成: ${successCount}/${results.length} 成功`)
      } catch (error) {
        this.$message.error(`批量查询异常: ${error.message}`)
      } finally {
        this.loading.batch = false
      }
    },
    
    // 运行API测试
    async runTests() {
      this.testRunning = true
      try {
        this.$message.info('开始运行API测试套件...')
        const results = await apiTest.runAllTests()
        this.testResults = results
        
        if (results.failed === 0) {
          this.$message.success('所有API测试都通过了！')
        } else {
          this.$message.warning(`API测试完成，${results.failed} 个测试失败`)
        }
      } catch (error) {
        this.$message.error(`测试运行异常: ${error.message}`)
      } finally {
        this.testRunning = false
      }
    },
    
    // 添加结果到列表
    addResult(result) {
      this.results.unshift({
        ...result,
        timestamp: new Date().toLocaleTimeString()
      })
      
      // 限制结果数量
      if (this.results.length > 20) {
        this.results = this.results.slice(0, 20)
      }
    },
    
    // 显示数据详情
    showData(data) {
      this.selectedData = data
      this.dataDialogVisible = true
    }
  }
}
</script>

<style scoped>
.price-query-demo {
  padding: 20px;
}

.demo-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.demo-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.demo-section:last-child {
  border-bottom: none;
}

.test-details {
  margin-top: 10px;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}
</style>
