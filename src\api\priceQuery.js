import { sendSoapRequest } from "./request.js";

/**
 * 物价查询API - MES0061
 * 根据别名和交易代码查询物价信息
 */

/**
 * 查询物价信息
 * @param {string} alias - 别名 (如: MR)
 * @param {string} tradeCode - 交易代码 (如: 9100)
 * @param {Object} options - 额外选项
 * @returns {Promise<Object>} 物价查询结果
 */
export async function queryPrice(alias = "MR", tradeCode = "9100", options = {}) {
  try {
    // 构建请求消息XML
    const requestMessage = `<Request><Alias>${alias}</Alias><TradeCode>${tradeCode}</TradeCode></Request>`;

    // 发送SOAP请求
    const result = await sendSoapRequest("MES0061", requestMessage, options);

    if (result.success) {
      console.log("物价查询成功:", result.data);
      return {
        success: true,
        data: result.data,
        message: "物价查询成功",
      };
    } else {
      console.error("物价查询失败:", result.error);
      return {
        success: false,
        error: result.error,
        message: result.message || "物价查询失败",
      };
    }
  } catch (error) {
    console.error("物价查询异常:", error);
    return {
      success: false,
      error: "查询异常",
      message: error.message || "物价查询过程中发生异常",
    };
  }
}

// 导出所有API方法
export default {
  queryPrice,
};
