/**
 * API统一入口文件
 * 导出所有API模块，方便在组件中使用
 */

// 导入基础请求模块
import soapClient, { sendSoapRequest } from './request.js'

// 导入具体业务API模块
import priceQueryAPI from './priceQuery.js'

// 统一导出所有API
export {
  // 基础请求方法
  soapClient,
  sendSoapRequest,
  
  // 物价查询相关API
  priceQueryAPI
}

// 也可以按业务模块分组导出
export const API = {
  // 基础请求
  soap: {
    client: soapClient,
    sendRequest: sendSoapRequest
  },
  
  // 物价查询
  price: priceQueryAPI,
  
  // 后续可以添加更多业务模块
  // patient: patientAPI,
  // doctor: doctorAPI,
  // appointment: appointmentAPI,
}

// 默认导出API对象
export default API
