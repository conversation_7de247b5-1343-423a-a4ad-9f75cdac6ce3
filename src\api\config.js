/**
 * API配置文件
 * 统一管理所有API相关的配置参数
 */

// 环境配置
const ENV_CONFIG = {
  development: {
    baseURL: 'https://172.16.8.4:1443/csp/hsb/DHC.Published.ServiceForHATM.BS.ServiceForHATM.CLS',
    timeout: 30000,
    rejectUnauthorized: false, // 开发环境忽略SSL证书
    enableLogging: true
  },
  production: {
    baseURL: 'https://172.16.8.4:1443/csp/hsb/DHC.Published.ServiceForHATM.BS.ServiceForHATM.CLS',
    timeout: 15000,
    rejectUnauthorized: true, // 生产环境验证SSL证书
    enableLogging: false
  },
  test: {
    baseURL: 'https://test.example.com/soap/endpoint',
    timeout: 10000,
    rejectUnauthorized: false,
    enableLogging: true
  }
}

// 获取当前环境配置
const currentEnv = process.env.NODE_ENV || 'development'
export const API_CONFIG = ENV_CONFIG[currentEnv]

// SOAP请求配置
export const SOAP_CONFIG = {
  headers: {
    'Content-Type': 'text/xml; charset=utf-8',
    'SOAPAction': 'http://www.dhcc.com.cn/DHC.Published.ServiceForHATM.BS.ServiceForHATM.HIPMessageServer'
  },
  namespace: 'http://www.dhcc.com.cn',
  envelope: {
    soap: 'http://schemas.xmlsoap.org/soap/envelope/'
  }
}

// API动作类型配置
export const API_ACTIONS = {
  PRICE_QUERY: 'MES0061',        // 物价查询
  PATIENT_INFO: 'MES0001',       // 患者信息查询 (示例)
  DOCTOR_SCHEDULE: 'MES0002',    // 医生排班查询 (示例)
  APPOINTMENT: 'MES0003',        // 预约挂号 (示例)
  // 可以根据实际API文档添加更多动作类型
}

// 科室代码映射
export const DEPARTMENT_MAPPING = {
  'MR': { alias: 'MR', tradeCode: '9100', name: '磁共振科' },
  'CT': { alias: 'CT', tradeCode: '9101', name: 'CT科' },
  'DR': { alias: 'DR', tradeCode: '9102', name: 'DR科' },
  'US': { alias: 'US', tradeCode: '9103', name: '超声科' },
  'XR': { alias: 'XR', tradeCode: '9104', name: 'X光科' },
  // 可以根据实际业务需要添加更多科室
}

// 错误代码映射
export const ERROR_CODES = {
  '0000': '成功',
  '1001': '参数错误',
  '1002': '数据不存在',
  '1003': '系统繁忙',
  '1004': '权限不足',
  '1005': '网络超时',
  '9999': '未知错误'
}

// 请求重试配置
export const RETRY_CONFIG = {
  maxRetries: 3,           // 最大重试次数
  retryDelay: 1000,        // 重试延迟(毫秒)
  retryCondition: (error) => {
    // 定义哪些错误需要重试
    return error.code === 'ECONNABORTED' || 
           error.code === 'ENOTFOUND' ||
           (error.response && error.response.status >= 500)
  }
}

// 缓存配置
export const CACHE_CONFIG = {
  enabled: true,
  defaultTTL: 5 * 60 * 1000,  // 默认缓存5分钟
  maxSize: 100,               // 最大缓存条目数
  keyPrefix: 'hospital_api_'  // 缓存键前缀
}

// 日志配置
export const LOG_CONFIG = {
  enabled: API_CONFIG.enableLogging,
  level: currentEnv === 'production' ? 'error' : 'debug',
  includeRequest: true,
  includeResponse: true,
  maxLogLength: 1000  // 最大日志长度
}

// 默认请求参数
export const DEFAULT_PARAMS = {
  price: {
    alias: 'MR',
    tradeCode: '9100'
  }
}

// API版本信息
export const API_VERSION = {
  current: '1.0.0',
  supported: ['1.0.0'],
  deprecated: []
}

// 导出所有配置
export default {
  API_CONFIG,
  SOAP_CONFIG,
  API_ACTIONS,
  DEPARTMENT_MAPPING,
  ERROR_CODES,
  RETRY_CONFIG,
  CACHE_CONFIG,
  LOG_CONFIG,
  DEFAULT_PARAMS,
  API_VERSION
}
