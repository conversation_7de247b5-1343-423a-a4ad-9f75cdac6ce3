/**
 * API配置文件
 * 统一管理所有API相关的配置参数
 */

// 环境配置
const ENV_CONFIG = {
  development: {
    baseURL: 'https://**********:1443/csp/hsb/DHC.Published.ServiceForHATM.BS.ServiceForHATM.CLS',
    timeout: 30000,
    rejectUnauthorized: false // 开发环境忽略SSL证书
  },
  production: {
    baseURL: 'https://**********:1443/csp/hsb/DHC.Published.ServiceForHATM.BS.ServiceForHATM.CLS',
    timeout: 15000,
    rejectUnauthorized: true // 生产环境验证SSL证书
  }
}

// 获取当前环境配置
const currentEnv = process.env.NODE_ENV || 'development'
export const API_CONFIG = ENV_CONFIG[currentEnv]

// SOAP请求配置
export const SOAP_CONFIG = {
  headers: {
    'Content-Type': 'text/xml; charset=utf-8',
    'SOAPAction': 'http://www.dhcc.com.cn/DHC.Published.ServiceForHATM.BS.ServiceForHATM.HIPMessageServer'
  },
  namespace: 'http://www.dhcc.com.cn',
  envelope: {
    soap: 'http://schemas.xmlsoap.org/soap/envelope/'
  }
}

// API动作类型配置 (根据接口文档)
export const API_ACTIONS = {
  QUERY_DEPARTMENT: 'MES0018',    // 查询科室列表 (1012)
  QUERY_DOCTOR: 'MES0038',        // 查询医生列表 (1013)
  QUERY_SCHEDULE: 'MES0039',      // 查询排班记录 (1004)
  QUERY_TIME_INFO: 'MES0040'      // 查询医生号源分时信息 (10041)
}

// 错误代码映射
export const ERROR_CODES = {
  '0': '成功',
  '1001': '参数错误',
  '1002': '数据不存在',
  '1003': '系统繁忙',
  '1004': '权限不足',
  '1005': '网络超时',
  '9999': '未知错误'
}

// 导出所有配置
export default {
  API_CONFIG,
  SOAP_CONFIG,
  API_ACTIONS,
  ERROR_CODES
}
